<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth; // Added Auth facade
use App\Http\Controllers\Auth\GoogleLoginController; // Added GoogleLoginController
use App\Http\Controllers\Admin\SettingsController;
use App\Http\Controllers\Admin\PlanController; // Added PlanController import
use App\Http\Controllers\Admin\TransactionController; // Added TransactionController import
use App\Http\Controllers\Admin\PosterController; // Added PosterController import
use App\Http\Controllers\Admin\FrameController; // Added FrameController import

// Root route
Route::get('/', function () {
    return redirect()->route('login');
});


// Admin Login Routes
Route::prefix('admin')->name('admin.')->group(function () {
    // Admin Login Form
    Route::get('login', function () {
        if (Auth::check()) {
            return redirect()->route('admin.dashboard');
        }
        return view('admin.login');
    })->name('login');

    // Admin Login Process
    Route::post('login', function (\Illuminate\Http\Request $request) {
        $credentials = $request->validate([
            'email' => 'required|email',
            'password' => 'required',
        ]);

        if (Auth::attempt($credentials, $request->boolean('remember'))) {
            $request->session()->regenerate();
            return redirect()->intended(route('admin.dashboard'));
        }

        return back()->withErrors([
            'email' => 'The provided credentials do not match our records.',
        ])->onlyInput('email');
    })->name('login.process');

    // Admin Logout
    Route::post('logout', function (\Illuminate\Http\Request $request) {
        Auth::logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();
        return redirect()->route('admin.login');
    })->name('logout');
});

// Protected Admin Routes
Route::prefix('admin')->name('admin.')->middleware('auth')->group(function () {
    // Admin Dashboard
    Route::get('/', function () {
        return view('admin.dashboard');
    })->name('dashboard');

    Route::get('dashboard', function () {
        return view('admin.dashboard');
    })->name('dashboard');

    // Admin Settings Routes
    Route::get('settings', [SettingsController::class, 'index'])->name('settings.index');
    Route::post('settings', [SettingsController::class, 'store'])->name('settings.store');

    // Admin Plans Routes
    Route::resource('plans', PlanController::class)->except(['show'])->names('plans');

    // Admin Transactions Route
    Route::get('transactions', [TransactionController::class, 'index'])->name('transactions.index');

    // Admin Posters Routes
    Route::resource('posters', PosterController::class)->except(['show'])->names('posters');

    // Admin Frames Routes
    Route::resource('frames', FrameController::class)->except(['show'])->names('frames');
});

// Google OAuth Routes
Route::get('/auth/google', [GoogleLoginController::class, 'redirectToGoogle'])->name('auth.google');
Route::get('/auth/callback/google', [GoogleLoginController::class, 'handleGoogleCallback'])->name('auth.google.callback');

// Debug route to check OAuth configuration
Route::get('/debug/google-config', function () {
    return response()->json([
        'google_client_id' => config('services.google.client_id'),
        'google_redirect_url' => config('services.google.redirect'),
        'app_url' => config('app.url'),
        'route_url' => route('auth.google.callback'),
        'current_url' => request()->getSchemeAndHttpHost(),
        'socialite_redirect_url' => \Laravel\Socialite\Facades\Socialite::driver('google')->getRedirectUrl(),
    ]);
});

// Test route to check if Google OAuth is working
Route::get('/test/google-auth', function () {
    try {
        // Just check if config is available
        return response()->json([
            'status' => 'success',
            'message' => 'Google OAuth configuration is available',
            'redirect_url' => config('services.google.redirect'),
            'client_id' => config('services.google.client_id'),
            'client_secret_set' => !empty(config('services.google.client_secret')),
        ]);
    } catch (Exception $e) {
        return response()->json([
            'status' => 'error',
            'message' => 'Google OAuth configuration error',
            'error' => $e->getMessage(),
        ], 500);
    }
});

// Simple Google OAuth redirect without extra processing
Route::get('/auth/google-simple', function () {
    try {
        $url = 'https://accounts.google.com/o/oauth2/auth?' . http_build_query([
            'client_id' => config('services.google.client_id'),
            'redirect_uri' => config('services.google.redirect'),
            'scope' => 'openid profile email',
            'response_type' => 'code',
            'state' => \Str::random(40),
        ]);

        return redirect($url);
    } catch (Exception $e) {
        return redirect()->route('login')->with('error', 'Google OAuth error: ' . $e->getMessage());
    }
})->name('auth.google.simple');

// Helper auth routes for testing
Route::get('/login', function () {
    $html = "
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; }
        .btn { display: inline-block; padding: 12px 24px; margin: 10px 5px; text-decoration: none; border-radius: 5px; font-weight: bold; }
        .btn-google { background: #4285f4; color: white; }
        .btn-test { background: #34a853; color: white; }
        .info-box { background: #f5f5f5; padding: 15px; border-radius: 5px; margin: 20px 0; }
        .error { background: #ffebee; color: #c62828; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .success { background: #e8f5e8; color: #2e7d32; padding: 15px; border-radius: 5px; margin: 10px 0; }
    </style>
    <h1>🎨 Social Media Poster - Login</h1>

    <div class='info-box'>
        <h3>📋 Current Status:</h3>
        <p><strong>✅ Laravel Server:</strong> Running on " . config('app.url') . "</p>
        <p><strong>🔧 Database:</strong> SQLite Connected</p>
        <p><strong>🔑 Google Client ID:</strong> " . (config('services.google.client_id') ?: 'Not Set') . "</p>
        <p><strong>🔄 Redirect URL:</strong> " . config('services.google.redirect') . "</p>
    </div>

    <h3>🚀 Login Options:</h3>
    <a href='" . route('auth.google') . "' class='btn btn-google'>🔐 Login with Google (Socialite)</a>
    <a href='" . route('auth.google.simple') . "' class='btn btn-test'>🔐 Login with Google (Direct)</a>
    <a href='https://accounts.google.com/o/oauth2/auth?client_id=************-6b6apt6btmoo7k7ni99gnljj2uh5b72f.apps.googleusercontent.com&redirect_uri=http%3A%2F%2F127.0.0.1%3A8000%2Fauth%2Fcallback%2Fgoogle&scope=openid+profile+email&response_type=code&state=test123' class='btn' style='background: #ff5722; color: white;'>🔐 Direct Google OAuth Link</a>

    <h3>🔧 Debug Tools:</h3>
    <a href='/test/google-auth' target='_blank' class='btn' style='background: #ff9800; color: white;'>Test OAuth Config</a>

    <div class='info-box'>
        <h4>❗ If you get 'OAuth client was deleted' error:</h4>
        <ol>
            <li>Go to <a href='https://console.cloud.google.com/' target='_blank'>Google Cloud Console</a></li>
            <li>Navigate to APIs & Services → Credentials</li>
            <li>Create new OAuth 2.0 Client ID</li>
            <li>Add redirect URI: <code>http://localhost:8000/auth/callback/google</code></li>
            <li>Update .env file with new credentials</li>
        </ol>
    </div>
    ";
    if (session('error')) { $html = '<div style="color: red; background: #ffebee; padding: 10px; margin: 10px 0;">' . session('error') . '</div>' . $html; }
    if (session('status')) { $html = '<div style="color: green; background: #e8f5e8; padding: 10px; margin: 10px 0;">' . session('status') . '</div>' . $html; }
    return $html;
})->name('login');

Route::get('/home', function () {
    if (Auth::check()) {
        return "Welcome home, " . Auth::user()->name . "! Email: " . Auth::user()->email . " <form method='POST' action='" . route('logout') . "'><input type='hidden' name='_token' value='" . csrf_token() . "'><button type='submit'>Logout</button></form>";
    }
    return redirect()->route('login');
})->middleware('auth')->name('home'); // 'auth' middleware will redirect to 'login' if not authenticated

Route::post('/logout', function () {
    Auth::logout();
    session()->invalidate(); // Invalidate session
    session()->regenerateToken(); // Regenerate CSRF token
    return redirect('/login')->with('status', 'Successfully logged out!');
})->name('logout');
