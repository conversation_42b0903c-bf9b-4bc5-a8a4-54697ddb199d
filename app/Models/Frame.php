<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Frame extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'image_path',
        'is_premium',
        'status',
        'category_id',
        'subcategory_id',
    ];

    /**
     * Get the category that owns the frame.
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class, 'category_id');
    }

    /**
     * Get the subcategory that owns the frame.
     */
    public function subcategory(): BelongsTo
    {
        return $this->belongsTo(Category::class, 'subcategory_id');
    }

    /**
     * Get the full category path (Category > Subcategory).
     */
    public function getCategoryPathAttribute(): string
    {
        if ($this->subcategory) {
            return $this->category->name . ' > ' . $this->subcategory->name;
        } elseif ($this->category) {
            return $this->category->name;
        }

        return 'Uncategorized';
    }

    /**
     * Scope to filter by category.
     */
    public function scopeByCategory($query, $categoryId)
    {
        if ($categoryId) {
            return $query->where('category_id', $categoryId);
        }
        return $query;
    }

    /**
     * Scope to filter by subcategory.
     */
    public function scopeBySubcategory($query, $subcategoryId)
    {
        if ($subcategoryId) {
            return $query->where('subcategory_id', $subcategoryId);
        }
        return $query;
    }

    /**
     * Scope to filter by premium status.
     */
    public function scopeByPremium($query, $isPremium)
    {
        if ($isPremium !== null && $isPremium !== '') {
            return $query->where('is_premium', $isPremium);
        }
        return $query;
    }

    /**
     * Scope to filter by status.
     */
    public function scopeByStatus($query, $status)
    {
        if ($status !== null && $status !== '') {
            return $query->where('status', $status);
        }
        return $query;
    }

    /**
     * Scope to search by name.
     */
    public function scopeSearchByName($query, $name)
    {
        if ($name) {
            return $query->where('name', 'like', '%' . $name . '%');
        }
        return $query;
    }
}
