<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Frame; // Changed to Frame model
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class FrameController extends Controller // Changed class name
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $frames = Frame::latest()->paginate(15); // Changed to Frame model
        return view('admin.frames.index', compact('frames')); // Changed view path
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.frames.create'); // Changed view path
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'image' => 'required|image|mimes:jpeg,png,jpg,gif,svg,webp|max:102400', // 100MB = 102400KB
            'is_premium' => 'nullable|boolean',
        ]);

        $imagePath = null;
        if ($request->hasFile('image')) {
            $image = $request->file('image');
            $imageName = Str::slug($request->input('name')) . '-' . time() . '.' . $image->getClientOriginalExtension();
            // Store in 'public/frames' which links to 'storage/app/public/frames'
            $imagePath = $image->storeAs('frames', $imageName, 'public'); // Changed storage path
        }

        Frame::create([ // Changed to Frame model
            'name' => $request->input('name'),
            'image_path' => $imagePath,
            'is_premium' => $request->has('is_premium') ? true : false,
        ]);

        return redirect()->route('admin.frames.index')->with('success', 'Frame uploaded successfully.'); // Changed route and message
    }

    /**
     * Display the specified resource.
     */
    public function show(Frame $frame) // Changed type hint
    {
        return redirect()->route('admin.frames.index'); // Changed route
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Frame $frame) // Changed type hint
    {
        return view('admin.frames.edit', compact('frame')); // Changed view path
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Frame $frame) // Changed type hint
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg,webp|max:102400', // 100MB = 102400KB
            'is_premium' => 'nullable|boolean',
        ]);

        $imagePath = $frame->image_path;

        if ($request->hasFile('image')) {
            if ($frame->image_path && Storage::disk('public')->exists($frame->image_path)) {
                Storage::disk('public')->delete($frame->image_path);
            }
            $image = $request->file('image');
            $imageName = Str::slug($request->input('name')) . '-' . time() . '.' . $image->getClientOriginalExtension();
            $imagePath = $image->storeAs('frames', $imageName, 'public'); // Changed storage path
        }

        $frame->update([ // Changed to Frame model
            'name' => $request->input('name'),
            'image_path' => $imagePath,
            'is_premium' => $request->has('is_premium') ? true : false,
        ]);

        return redirect()->route('admin.frames.index')->with('success', 'Frame updated successfully.'); // Changed route and message
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Frame $frame) // Changed type hint
    {
        if ($frame->image_path && Storage::disk('public')->exists($frame->image_path)) {
            Storage::disk('public')->delete($frame->image_path);
        }
        $frame->delete(); // Changed to Frame model

        return redirect()->route('admin.frames.index')->with('success', 'Frame deleted successfully.'); // Changed route and message
    }

    /**
     * Toggle the status of the specified resource.
     */
    public function toggleStatus(Frame $frame)
    {
        $frame->update([
            'status' => !$frame->status
        ]);

        return response()->json([
            'success' => true,
            'status' => $frame->status,
            'message' => 'Frame status updated successfully.'
        ]);
    }
}
