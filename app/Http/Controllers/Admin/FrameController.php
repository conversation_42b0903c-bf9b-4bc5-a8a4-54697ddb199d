<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Frame; // Changed to Frame model
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class FrameController extends Controller // Changed class name
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $frames = Frame::latest()->paginate(15); // Changed to Frame model
        return view('admin.frames.index', compact('frames')); // Changed view path
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.frames.create'); // Changed view path
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // Get the maximum upload size from PHP configuration
        $maxUploadSize = min(
            $this->parseSize(ini_get('upload_max_filesize')),
            $this->parseSize(ini_get('post_max_size'))
        );

        // Convert to KB for validation (<PERSON><PERSON> expects KB)
        $maxUploadSizeKB = floor($maxUploadSize / 1024);

        $request->validate([
            'name' => 'required|string|max:255',
            'image' => 'required|image|mimes:jpeg,png,jpg,gif,svg,webp|max:' . $maxUploadSizeKB,
            'is_premium' => 'nullable|boolean',
        ]);

        $imagePath = null;
        if ($request->hasFile('image')) {
            $image = $request->file('image');

            // Check if file upload was successful
            if (!$image->isValid()) {
                return back()->withErrors(['image' => 'The image failed to upload. Please try again.']);
            }

            $imageName = Str::slug($request->input('name')) . '-' . time() . '.' . $image->getClientOriginalExtension();

            try {
                // Store in 'public/frames' which links to 'storage/app/public/frames'
                $imagePath = $image->storeAs('frames', $imageName, 'public'); // Changed storage path

                if (!$imagePath) {
                    return back()->withErrors(['image' => 'Failed to save the image. Please check storage permissions.']);
                }
            } catch (\Exception $e) {
                return back()->withErrors(['image' => 'Error uploading image: ' . $e->getMessage()]);
            }
        } else {
            return back()->withErrors(['image' => 'No image file was uploaded.']);
        }

        Frame::create([ // Changed to Frame model
            'name' => $request->input('name'),
            'image_path' => $imagePath,
            'is_premium' => $request->has('is_premium') ? true : false,
            'status' => true, // Default to active
        ]);

        return redirect()->route('admin.frames.index')->with('success', 'Frame uploaded successfully.'); // Changed route and message
    }

    /**
     * Display the specified resource.
     */
    public function show(Frame $frame) // Changed type hint
    {
        return redirect()->route('admin.frames.index'); // Changed route
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Frame $frame) // Changed type hint
    {
        return view('admin.frames.edit', compact('frame')); // Changed view path
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Frame $frame) // Changed type hint
    {
        // Get the maximum upload size from PHP configuration
        $maxUploadSize = min(
            $this->parseSize(ini_get('upload_max_filesize')),
            $this->parseSize(ini_get('post_max_size'))
        );

        // Convert to KB for validation (Laravel expects KB)
        $maxUploadSizeKB = floor($maxUploadSize / 1024);

        $request->validate([
            'name' => 'required|string|max:255',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg,webp|max:' . $maxUploadSizeKB,
            'is_premium' => 'nullable|boolean',
        ]);

        $imagePath = $frame->image_path;

        if ($request->hasFile('image')) {
            if ($frame->image_path && Storage::disk('public')->exists($frame->image_path)) {
                Storage::disk('public')->delete($frame->image_path);
            }
            $image = $request->file('image');
            $imageName = Str::slug($request->input('name')) . '-' . time() . '.' . $image->getClientOriginalExtension();
            $imagePath = $image->storeAs('frames', $imageName, 'public'); // Changed storage path
        }

        $frame->update([ // Changed to Frame model
            'name' => $request->input('name'),
            'image_path' => $imagePath,
            'is_premium' => $request->has('is_premium') ? true : false,
        ]);

        return redirect()->route('admin.frames.index')->with('success', 'Frame updated successfully.'); // Changed route and message
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Frame $frame) // Changed type hint
    {
        if ($frame->image_path && Storage::disk('public')->exists($frame->image_path)) {
            Storage::disk('public')->delete($frame->image_path);
        }
        $frame->delete(); // Changed to Frame model

        return redirect()->route('admin.frames.index')->with('success', 'Frame deleted successfully.'); // Changed route and message
    }

    /**
     * Toggle the status of the specified resource.
     */
    public function toggleStatus(Frame $frame)
    {
        $frame->update([
            'status' => !$frame->status
        ]);

        return response()->json([
            'success' => true,
            'status' => $frame->status,
            'message' => 'Frame status updated successfully.'
        ]);
    }

    /**
     * Parse size string to bytes
     */
    private function parseSize($size)
    {
        $unit = preg_replace('/[^bkmgtpezy]/i', '', $size);
        $size = preg_replace('/[^0-9\.]/', '', $size);

        if ($unit) {
            return round($size * pow(1024, stripos('bkmgtpezy', $unit[0])));
        } else {
            return round($size);
        }
    }
}
