<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Poster;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage; // Import Storage facade
use Illuminate\Support\Str; // Import Str facade for generating unique filenames

class PosterController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $posters = Poster::latest()->paginate(15); // Paginate for better display
        return view('admin.posters.index', compact('posters'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.posters.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // Get the maximum upload size from PHP configuration
        $maxUploadSize = min(
            $this->parseSize(ini_get('upload_max_filesize')),
            $this->parseSize(ini_get('post_max_size'))
        );

        // Convert to KB for validation (<PERSON><PERSON> expects KB)
        $maxUploadSizeKB = floor($maxUploadSize / 1024);

        $request->validate([
            'name' => 'required|string|max:255',
            'image' => 'required|image|mimes:jpeg,png,jpg,gif,svg,webp|max:' . $maxUploadSizeKB,
            'is_premium' => 'nullable|boolean', // Checkbox might not be present if false
        ]);

        $imagePath = null;
        if ($request->hasFile('image')) {
            $image = $request->file('image');

            // Check if file upload was successful
            if (!$image->isValid()) {
                return back()->withErrors(['image' => 'The image failed to upload. Please try again.']);
            }

            // Generate a unique name for the image to prevent overwrites
            $imageName = Str::slug($request->input('name')) . '-' . time() . '.' . $image->getClientOriginalExtension();

            try {
                // Store in 'public/posters' which links to 'storage/app/public/posters'
                $imagePath = $image->storeAs('posters', $imageName, 'public');

                if (!$imagePath) {
                    return back()->withErrors(['image' => 'Failed to save the image. Please check storage permissions.']);
                }
            } catch (\Exception $e) {
                return back()->withErrors(['image' => 'Error uploading image: ' . $e->getMessage()]);
            }
        } else {
            return back()->withErrors(['image' => 'No image file was uploaded.']);
        }

        Poster::create([
            'name' => $request->input('name'),
            'image_path' => $imagePath,
            'is_premium' => $request->has('is_premium') ? true : false,
            'status' => true, // Default to active
        ]);

        return redirect()->route('admin.posters.index')->with('success', 'Poster uploaded successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Poster $poster)
    {
        // Typically not used for admin CRUD if excluded in routes
        return redirect()->route('admin.posters.index');
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Poster $poster)
    {
        return view('admin.posters.edit', compact('poster'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Poster $poster)
    {
        // Get the maximum upload size from PHP configuration
        $maxUploadSize = min(
            $this->parseSize(ini_get('upload_max_filesize')),
            $this->parseSize(ini_get('post_max_size'))
        );

        // Convert to KB for validation (Laravel expects KB)
        $maxUploadSizeKB = floor($maxUploadSize / 1024);

        $request->validate([
            'name' => 'required|string|max:255',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg,webp|max:' . $maxUploadSizeKB,
            'is_premium' => 'nullable|boolean',
        ]);

        $imagePath = $poster->image_path; // Keep old image if new one not uploaded

        if ($request->hasFile('image')) {
            // Delete old image if it exists
            if ($poster->image_path && Storage::disk('public')->exists($poster->image_path)) {
                Storage::disk('public')->delete($poster->image_path);
            }
            // Store new image
            $image = $request->file('image');
            $imageName = Str::slug($request->input('name')) . '-' . time() . '.' . $image->getClientOriginalExtension();
            $imagePath = $image->storeAs('posters', $imageName, 'public');
        }

        $poster->update([
            'name' => $request->input('name'),
            'image_path' => $imagePath,
            'is_premium' => $request->has('is_premium') ? true : false,
        ]);

        return redirect()->route('admin.posters.index')->with('success', 'Poster updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Poster $poster)
    {
        // Delete the image file from storage
        if ($poster->image_path && Storage::disk('public')->exists($poster->image_path)) {
            Storage::disk('public')->delete($poster->image_path);
        }

        // Delete the poster record from the database
        $poster->delete();

        return redirect()->route('admin.posters.index')->with('success', 'Poster deleted successfully.');
    }

    /**
     * Toggle the status of the specified resource.
     */
    public function toggleStatus(Poster $poster)
    {
        $poster->update([
            'status' => !$poster->status
        ]);

        return response()->json([
            'success' => true,
            'status' => $poster->status,
            'message' => 'Poster status updated successfully.'
        ]);
    }

    /**
     * Parse size string to bytes
     */
    private function parseSize($size)
    {
        $unit = preg_replace('/[^bkmgtpezy]/i', '', $size);
        $size = preg_replace('/[^0-9\.]/', '', $size);

        if ($unit) {
            return round($size * pow(1024, stripos('bkmgtpezy', $unit[0])));
        } else {
            return round($size);
        }
    }
}
