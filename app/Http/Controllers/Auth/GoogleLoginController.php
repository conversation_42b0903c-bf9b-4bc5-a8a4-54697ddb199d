<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Laravel\Socialite\Facades\Socialite;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Exception;

class GoogleLoginController extends Controller
{
    /**
     * Redirect the user to the Google authentication page.
     *
     * @return \Illuminate\Http\RedirectResponse|\Symfony\Component\HttpFoundation\RedirectResponse
     */
    public function redirectToGoogle()
    {
        try {
            return Socialite::driver('google')->redirect();
        } catch (Exception $e) {
            \Log::error('Google OAuth redirect error: ' . $e->getMessage());
            return redirect()->route('login')->with('error', 'Google authentication service is not available. Please try again later.');
        }
    }

    /**
     * Handle the Google OAuth callback.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function handleGoogleCallback()
    {
        try {
            \Log::info('Google OAuth callback received');
            \Log::info('Request URL: ' . request()->fullUrl());
            \Log::info('Request parameters: ' . json_encode(request()->all()));

            $googleUser = Socialite::driver('google')->user();

            \Log::info('Google user retrieved: ' . $googleUser->getEmail());

            // Find or create user
            $user = User::where('email', $googleUser->getEmail())->first();

            if ($user) {
                // Update Google ID if not set
                if (!$user->google_id) {
                    $user->update(['google_id' => $googleUser->getId()]);
                }
                \Log::info('Existing user logged in: ' . $user->email);
            } else {
                // Create new user
                $user = User::create([
                    'name' => $googleUser->getName(),
                    'email' => $googleUser->getEmail(),
                    'google_id' => $googleUser->getId(),
                    'email_verified_at' => now(),
                ]);
                \Log::info('New user created: ' . $user->email);
            }

            // Log the user in
            Auth::login($user, true);

            return redirect()->route('home')->with('status', 'Successfully logged in with Google!');

        } catch (Exception $e) {
            \Log::error('Google OAuth callback error: ' . $e->getMessage());
            \Log::error('Error trace: ' . $e->getTraceAsString());

            $errorMessage = 'Google authentication failed';
            if (str_contains($e->getMessage(), 'deleted_client')) {
                $errorMessage = 'Google OAuth client configuration error. Please check your Google Cloud Console settings.';
            } elseif (str_contains($e->getMessage(), 'redirect_uri_mismatch')) {
                $errorMessage = 'Redirect URI mismatch. Please ensure your Google Cloud Console has the correct redirect URI configured.';
            }

            return redirect()->route('login')->with('error', $errorMessage . ': ' . $e->getMessage());
        }
    }
}
