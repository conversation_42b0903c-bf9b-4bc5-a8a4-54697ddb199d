<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('posters', function (Blueprint $table) {
            $table->unsignedBigInteger('category_id')->nullable()->after('is_premium');
            $table->unsignedBigInteger('subcategory_id')->nullable()->after('category_id');

            // Foreign key constraints
            $table->foreign('category_id')->references('id')->on('categories')->onDelete('set null');
            $table->foreign('subcategory_id')->references('id')->on('categories')->onDelete('set null');

            // Indexes for better performance
            $table->index(['category_id', 'status']);
            $table->index(['subcategory_id', 'status']);
        });

        Schema::table('frames', function (Blueprint $table) {
            $table->unsignedBigInteger('category_id')->nullable()->after('is_premium');
            $table->unsignedBigInteger('subcategory_id')->nullable()->after('category_id');

            // Foreign key constraints
            $table->foreign('category_id')->references('id')->on('categories')->onDelete('set null');
            $table->foreign('subcategory_id')->references('id')->on('categories')->onDelete('set null');

            // Indexes for better performance
            $table->index(['category_id', 'status']);
            $table->index(['subcategory_id', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('posters', function (Blueprint $table) {
            $table->dropForeign(['category_id']);
            $table->dropForeign(['subcategory_id']);
            $table->dropIndex(['category_id', 'status']);
            $table->dropIndex(['subcategory_id', 'status']);
            $table->dropColumn(['category_id', 'subcategory_id']);
        });

        Schema::table('frames', function (Blueprint $table) {
            $table->dropForeign(['category_id']);
            $table->dropForeign(['subcategory_id']);
            $table->dropIndex(['category_id', 'status']);
            $table->dropIndex(['subcategory_id', 'status']);
            $table->dropColumn(['category_id', 'subcategory_id']);
        });
    }
};
