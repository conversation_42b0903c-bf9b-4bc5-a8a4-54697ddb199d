<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('categories', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('slug')->unique();
            $table->text('description')->nullable();
            $table->string('icon')->nullable(); // For emoji or icon class
            $table->unsignedBigInteger('parent_id')->nullable(); // For hierarchical structure
            $table->integer('sort_order')->default(0); // For custom ordering
            $table->boolean('status')->default(true); // Active/Inactive
            $table->timestamps();

            // Foreign key constraint for parent category
            $table->foreign('parent_id')->references('id')->on('categories')->onDelete('cascade');

            // Index for better performance
            $table->index(['parent_id', 'status']);
            $table->index('slug');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('categories');
    }
};
