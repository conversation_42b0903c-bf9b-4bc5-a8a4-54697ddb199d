<?php
// Simple PHP configuration checker for upload settings
echo "<h2>PHP Upload Configuration</h2>";
echo "<table border='1' cellpadding='5'>";
echo "<tr><th>Setting</th><th>Value</th></tr>";
echo "<tr><td>upload_max_filesize</td><td>" . ini_get('upload_max_filesize') . "</td></tr>";
echo "<tr><td>post_max_size</td><td>" . ini_get('post_max_size') . "</td></tr>";
echo "<tr><td>max_execution_time</td><td>" . ini_get('max_execution_time') . "</td></tr>";
echo "<tr><td>max_input_time</td><td>" . ini_get('max_input_time') . "</td></tr>";
echo "<tr><td>memory_limit</td><td>" . ini_get('memory_limit') . "</td></tr>";
echo "<tr><td>max_file_uploads</td><td>" . ini_get('max_file_uploads') . "</td></tr>";
echo "</table>";

echo "<h3>Storage Directory Check</h3>";
$storageDir = __DIR__ . '/../storage/app/public';
echo "<p>Storage directory: " . $storageDir . "</p>";
echo "<p>Directory exists: " . (is_dir($storageDir) ? 'Yes' : 'No') . "</p>";
echo "<p>Directory writable: " . (is_writable($storageDir) ? 'Yes' : 'No') . "</p>";

$postersDir = $storageDir . '/posters';
echo "<p>Posters directory exists: " . (is_dir($postersDir) ? 'Yes' : 'No') . "</p>";
echo "<p>Posters directory writable: " . (is_writable($postersDir) ? 'Yes' : 'No') . "</p>";

$framesDir = $storageDir . '/frames';
echo "<p>Frames directory exists: " . (is_dir($framesDir) ? 'Yes' : 'No') . "</p>";
echo "<p>Frames directory writable: " . (is_writable($framesDir) ? 'Yes' : 'No') . "</p>";

echo "<h3>Symlink Check</h3>";
$publicStorage = __DIR__ . '/storage';
echo "<p>Public storage symlink exists: " . (is_link($publicStorage) ? 'Yes' : 'No') . "</p>";
echo "<p>Public storage points to: " . (is_link($publicStorage) ? readlink($publicStorage) : 'N/A') . "</p>";
?>
