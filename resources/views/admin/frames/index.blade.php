@extends('layouts.admin')

@section('title', 'Frames Management')
@section('page-title', 'Frames Management')

@push('styles')
@include('admin.shared-styles')
@endpush

@section('content')
<div class="page-header">
    <h1>🖼️ Manage Frames</h1>
    <a href="{{ route('admin.frames.create') }}" class="btn btn-create">+ Add New Frame</a>
</div>

@if(session('success'))
    <div class="alert alert-success">
        {{ session('success') }}
    </div>
@endif

<div class="table-container">
    <table>
        <thead>
            <tr>
                <th>ID</th>
                <th>Image</th>
                <th>Name</th>
                <th>Premium</th>
                <th>Path</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            @forelse ($frames as $frame)
                <tr>
                    <td><strong>#{{ $frame->id }}</strong></td>
                    <td>
                        @if($frame->image_path)
                            <img src="{{ Storage::url($frame->image_path) }}" alt="{{ $frame->name }}" class="thumbnail">
                        @else
                            <span style="color: #64748b;">No Image</span>
                        @endif
                    </td>
                    <td><strong>{{ $frame->name }}</strong></td>
                    <td>
                        @if($frame->is_premium)
                            <span class="status-premium">Premium</span>
                        @else
                            <span class="status-regular">Regular</span>
                        @endif
                    </td>
                    <td><code>{{ $frame->image_path }}</code></td>
                    <td>
                        <a href="{{ route('admin.frames.edit', $frame->id) }}" class="btn btn-edit">Edit</a>
                        <form action="{{ route('admin.frames.destroy', $frame->id) }}" method="POST" style="display:inline;">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-delete" onclick="return confirm('Are you sure you want to delete this frame?');">Delete</button>
                        </form>
                    </td>
                </tr>
            @empty
                <tr>
                    <td colspan="6">
                        <div class="empty-state">
                            <h3>No frames found</h3>
                            <p>Start by uploading your first frame template.</p>
                            <a href="{{ route('admin.frames.create') }}" class="btn btn-create">Upload First Frame</a>
                        </div>
                    </td>
                </tr>
            @endforelse
        </tbody>
    </table>
</div>

<div class="pagination">
    {{ $frames->links() }}
</div>
@endsection
