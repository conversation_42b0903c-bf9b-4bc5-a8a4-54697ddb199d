@extends('layouts.admin')

@section('title', 'Frames Management')
@section('page-title', 'Frames Management')

@push('styles')
@include('admin.shared-styles')
<style>
    .toggle-switch {
        position: relative;
        display: inline-block;
        width: 60px;
        height: 34px;
    }

    .toggle-switch input {
        opacity: 0;
        width: 0;
        height: 0;
    }

    .slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ccc;
        transition: .4s;
        border-radius: 34px;
    }

    .slider:before {
        position: absolute;
        content: "";
        height: 26px;
        width: 26px;
        left: 4px;
        bottom: 4px;
        background-color: white;
        transition: .4s;
        border-radius: 50%;
    }

    input:checked + .slider {
        background-color: #10b981;
    }

    input:checked + .slider:before {
        transform: translateX(26px);
    }

    .serial-number {
        font-weight: 600;
        color: #667eea;
    }
</style>
@endpush

@section('content')
<div class="page-header">
    <h1>🖼️ Manage Frames</h1>
    <a href="{{ route('admin.frames.create') }}" class="btn btn-create">+ Add New Frame</a>
</div>

@if(session('success'))
    <div class="alert alert-success">
        {{ session('success') }}
    </div>
@endif

<div class="table-container">
    <table>
        <thead>
            <tr>
                <th>S.No</th>
                <th>Image</th>
                <th>Name</th>
                <th>Premium</th>
                <th>Status</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            @forelse ($frames as $index => $frame)
                <tr>
                    <td><span class="serial-number">{{ $frames->firstItem() + $index }}</span></td>
                    <td>
                        @if($frame->image_path)
                            <img src="{{ Storage::url($frame->image_path) }}" alt="{{ $frame->name }}" class="thumbnail">
                        @else
                            <span style="color: #64748b;">No Image</span>
                        @endif
                    </td>
                    <td><strong>{{ $frame->name }}</strong></td>
                    <td>
                        @if($frame->is_premium)
                            <span class="status-premium">Premium</span>
                        @else
                            <span class="status-regular">Regular</span>
                        @endif
                    </td>
                    <td>
                        <label class="toggle-switch">
                            <input type="checkbox" {{ $frame->status ? 'checked' : '' }}
                                   onchange="toggleStatus({{ $frame->id }}, this)">
                            <span class="slider"></span>
                        </label>
                    </td>
                    <td>
                        <a href="{{ route('admin.frames.edit', $frame->id) }}" class="btn btn-edit">Edit</a>
                        <form action="{{ route('admin.frames.destroy', $frame->id) }}" method="POST" style="display:inline;">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-delete" onclick="return confirm('Are you sure you want to delete this frame?');">Delete</button>
                        </form>
                    </td>
                </tr>
            @empty
                <tr>
                    <td colspan="6">
                        <div class="empty-state">
                            <h3>No frames found</h3>
                            <p>Start by uploading your first frame template.</p>
                            <a href="{{ route('admin.frames.create') }}" class="btn btn-create">Upload First Frame</a>
                        </div>
                    </td>
                </tr>
            @endforelse
        </tbody>
    </table>
</div>

<div class="pagination">
    {{ $frames->links() }}
</div>

@push('scripts')
<script>
function toggleStatus(frameId, checkbox) {
    const isChecked = checkbox.checked;

    fetch(`/admin/frames/${frameId}/toggle-status`, {
        method: 'PATCH',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            status: isChecked
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Show success message (optional)
            console.log(data.message);
        } else {
            // Revert checkbox state if failed
            checkbox.checked = !isChecked;
            alert('Failed to update status');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        // Revert checkbox state if failed
        checkbox.checked = !isChecked;
        alert('An error occurred while updating status');
    });
}
</script>
@endpush
@endsection
