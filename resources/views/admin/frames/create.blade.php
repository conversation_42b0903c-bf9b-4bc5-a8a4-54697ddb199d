@extends('layouts.admin')

@section('title', 'Create Frame')
@section('page-title', 'Create Frame')

@push('styles')
@include('admin.shared-styles')
@endpush

@section('content')
<div class="page-header">
    <h1>🖼️ Upload New Frame</h1>
    <a href="{{ route('admin.frames.index') }}" class="btn btn-back">← Back to Frames</a>
</div>

@if ($errors->any())
    <div class="alert alert-error">
        <ul>
            @foreach ($errors->all() as $error)
                <li>{{ $error }}</li>
            @endforeach
        </ul>
    </div>
@endif

<div class="form-container">
    <form action="{{ route('admin.frames.store') }}" method="POST" enctype="multipart/form-data">
        @csrf

        <div class="form-group">
            <label for="name">Frame Name</label>
            <input type="text" name="name" id="name" value="{{ old('name') }}" required>
        </div>

        <div class="form-group">
            <label for="image">Frame Image</label>
            <input type="file" name="image" id="image" required>
            <small style="color: #6b7280; font-size: 0.875rem;">Accepted formats: jpeg, png, jpg, gif, svg, webp. Max size: {{ ini_get('upload_max_filesize') }}.</small>
        </div>

        <div class="form-group">
            <label for="is_premium">
                <input type="checkbox" name="is_premium" id="is_premium" value="1" {{ old('is_premium') ? 'checked' : '' }}>
                Is this a Premium Frame?
            </label>
        </div>

        <div class="form-actions">
            <button type="submit" class="btn btn-create">Upload Frame</button>
            <a href="{{ route('admin.frames.index') }}" class="btn btn-back">Cancel</a>
        </div>
    </form>
</div>
@endsection
