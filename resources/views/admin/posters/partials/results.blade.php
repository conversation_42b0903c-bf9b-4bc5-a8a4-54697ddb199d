<div class="table-container">
    <table>
        <thead>
            <tr>
                <th>S.No</th>
                <th>Image</th>
                <th>Name</th>
                <th>Category</th>
                <th>Premium</th>
                <th>Status</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            @forelse ($posters as $index => $poster)
                <tr>
                    <td><span class="serial-number">{{ $posters->firstItem() + $index }}</span></td>
                    <td>
                        @if($poster->image_path)
                            <img src="{{ Storage::url($poster->image_path) }}" alt="{{ $poster->name }}" class="thumbnail">
                        @else
                            <span style="color: #64748b;">No Image</span>
                        @endif
                    </td>
                    <td><strong>{{ $poster->name }}</strong></td>
                    <td>
                        <div class="category-info">
                            @if($poster->category)
                                <span class="category-name">{{ $poster->category_path }}</span>
                            @else
                                <span style="color: #64748b;">Uncategorized</span>
                            @endif
                        </div>
                    </td>
                    <td>
                        @if($poster->is_premium)
                            <span class="status-premium">Premium</span>
                        @else
                            <span class="status-regular">Regular</span>
                        @endif
                    </td>
                    <td>
                        <label class="toggle-switch">
                            <input type="checkbox" {{ $poster->status ? 'checked' : '' }} 
                                   onchange="toggleStatus({{ $poster->id }}, this)">
                            <span class="slider"></span>
                        </label>
                    </td>
                    <td>
                        <a href="{{ route('admin.posters.edit', $poster->id) }}" class="btn btn-edit">Edit</a>
                        <form action="{{ route('admin.posters.destroy', $poster->id) }}" method="POST" style="display:inline;">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-delete" onclick="return confirm('Are you sure you want to delete this poster?');">Delete</button>
                        </form>
                    </td>
                </tr>
            @empty
                <tr>
                    <td colspan="7">
                        <div class="empty-state">
                            <h3>No posters found</h3>
                            <p>No posters match your current filters.</p>
                        </div>
                    </td>
                </tr>
            @endforelse
        </tbody>
    </table>
</div>

<div class="pagination">
    {{ $posters->appends(request()->query())->links() }}
</div>
