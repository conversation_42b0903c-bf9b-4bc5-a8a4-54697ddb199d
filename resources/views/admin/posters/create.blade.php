@extends('layouts.admin')

@section('title', 'Create Poster')
@section('page-title', 'Create Poster')

@push('styles')
@include('admin.shared-styles')
@endpush

@section('content')
<div class="page-header">
    <h1>🖼️ Upload New Poster</h1>
    <a href="{{ route('admin.posters.index') }}" class="btn btn-back">← Back to Posters</a>
</div>

@if ($errors->any())
    <div class="alert alert-error">
        <ul>
            @foreach ($errors->all() as $error)
                <li>{{ $error }}</li>
            @endforeach
        </ul>
    </div>
@endif

<div class="form-container">
    <form action="{{ route('admin.posters.store') }}" method="POST" enctype="multipart/form-data">
        @csrf

        <div class="form-group">
            <label for="name">Poster Name</label>
            <input type="text" name="name" id="name" value="{{ old('name') }}" required>
        </div>

        <div class="form-group">
            <label for="image">Poster Image</label>
            <input type="file" name="image" id="image" required>
            <small style="color: #6b7280; font-size: 0.875rem;">Accepted formats: jpeg, png, jpg, gif, svg, webp. Max size: {{ ini_get('upload_max_filesize') }}.</small>
        </div>

        <div class="form-group">
            <label for="category_id">Category *</label>
            <select name="category_id" id="category_id" required>
                <option value="">-- Select Category --</option>
                @foreach(\App\Models\Category::parents()->active()->orderBy('name')->get() as $category)
                    <option value="{{ $category->id }}" {{ old('category_id') == $category->id ? 'selected' : '' }}>
                        {{ $category->name }}
                    </option>
                @endforeach
            </select>
        </div>

        <div class="form-group">
            <label for="subcategory_id">Subcategory</label>
            <select name="subcategory_id" id="subcategory_id" disabled>
                <option value="">-- Select Subcategory (Optional) --</option>
            </select>
        </div>

        <div class="form-group">
            <label for="is_premium">
                <input type="checkbox" name="is_premium" id="is_premium" value="1" {{ old('is_premium') ? 'checked' : '' }}>
                Is this a Premium Poster?
            </label>
        </div>

        <div class="form-actions">
            <button type="submit" class="btn btn-create">Upload Poster</button>
            <a href="{{ route('admin.posters.index') }}" class="btn btn-back">Cancel</a>
        </div>
    </form>
</div>

@include('admin.shared-category-script')
@endsection
