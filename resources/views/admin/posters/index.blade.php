@extends('layouts.admin')

@section('title', 'Posters Management')
@section('page-title', 'Posters Management')

@push('styles')
@include('admin.shared-styles')
<style>
    .toggle-switch {
        position: relative;
        display: inline-block;
        width: 60px;
        height: 34px;
    }

    .toggle-switch input {
        opacity: 0;
        width: 0;
        height: 0;
    }

    .slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ccc;
        transition: .4s;
        border-radius: 34px;
    }

    .slider:before {
        position: absolute;
        content: "";
        height: 26px;
        width: 26px;
        left: 4px;
        bottom: 4px;
        background-color: white;
        transition: .4s;
        border-radius: 50%;
    }

    input:checked + .slider {
        background-color: #10b981;
    }

    input:checked + .slider:before {
        transform: translateX(26px);
    }

    .serial-number {
        font-weight: 600;
        color: #667eea;
    }
</style>
@endpush

@section('content')
<div class="page-header">
    <h1>🖼️ Manage Posters</h1>
    <div class="header-actions">
        <span class="filter-indicator">
            Filters Applied: <span id="filter-count" style="display: none;">0</span>
        </span>
        <a href="{{ route('admin.posters.create') }}" class="btn btn-create">+ Add New Poster</a>
    </div>
</div>

@if(session('success'))
    <div class="alert alert-success">
        {{ session('success') }}
    </div>
@endif

<!-- Advanced Filters -->
<div class="filters-container" style="background: #f8fafc; padding: 1.5rem; border-radius: 8px; margin-bottom: 2rem;">
    <form id="filter-form" class="filters-form">
        <div class="filters-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
            <div class="form-group">
                <label for="name">Search by Name</label>
                <input type="text" name="name" id="name" value="{{ request('name') }}" placeholder="Enter poster name...">
            </div>

            <div class="form-group">
                <label for="filter_category_id">Category</label>
                <select name="category_id" id="filter_category_id">
                    <option value="">-- All Categories --</option>
                    @foreach($categories as $category)
                        <option value="{{ $category->id }}" {{ request('category_id') == $category->id ? 'selected' : '' }}>
                            {{ $category->name }}
                        </option>
                    @endforeach
                </select>
            </div>

            <div class="form-group">
                <label for="filter_subcategory_id">Subcategory</label>
                <select name="subcategory_id" id="filter_subcategory_id" disabled>
                    <option value="">-- All Subcategories --</option>
                    @foreach($subcategories as $subcategory)
                        <option value="{{ $subcategory->id }}" {{ request('subcategory_id') == $subcategory->id ? 'selected' : '' }}>
                            {{ $subcategory->name }}
                        </option>
                    @endforeach
                </select>
            </div>

            <div class="form-group">
                <label for="is_premium">Premium Status</label>
                <select name="is_premium" id="is_premium">
                    <option value="">-- All --</option>
                    <option value="1" {{ request('is_premium') === '1' ? 'selected' : '' }}>Premium</option>
                    <option value="0" {{ request('is_premium') === '0' ? 'selected' : '' }}>Regular</option>
                </select>
            </div>

            <div class="form-group">
                <label for="status">Status</label>
                <select name="status" id="status">
                    <option value="">-- All --</option>
                    <option value="1" {{ request('status') === '1' ? 'selected' : '' }}>Active</option>
                    <option value="0" {{ request('status') === '0' ? 'selected' : '' }}>Inactive</option>
                </select>
            </div>

            <div class="form-group" style="display: flex; align-items: end;">
                <button type="button" id="clear-filters" class="btn btn-back" style="width: 100%;">Clear Filters</button>
            </div>
        </div>
    </form>
</div>

<div id="results-container">
    @include('admin.posters.partials.results', ['posters' => $posters])
</div>


@include('admin.shared-category-script')

@push('scripts')
<script>
function toggleStatus(posterId, checkbox) {
    const isChecked = checkbox.checked;

    fetch(`/admin/posters/${posterId}/toggle-status`, {
        method: 'PATCH',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            status: isChecked
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log(data.message);
        } else {
            checkbox.checked = !isChecked;
            alert('Failed to update status');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        checkbox.checked = !isChecked;
        alert('An error occurred while updating status');
    });
}

// Override the initializeToggleSwitches function for this page
function initializeToggleSwitches() {
    // Toggle switches are already handled by the toggleStatus function
    // This function is called after AJAX updates to reinitialize any new elements
}
</script>
@endpush
@endsection
