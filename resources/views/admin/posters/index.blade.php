@extends('layouts.admin')

@section('title', 'Posters Management')
@section('page-title', 'Posters Management')

@push('styles')
@include('admin.shared-styles')
@endpush

@section('content')
<div class="page-header">
    <h1>🖼️ Manage Posters</h1>
    <a href="{{ route('admin.posters.create') }}" class="btn btn-create">+ Add New Poster</a>
</div>

@if(session('success'))
    <div class="alert alert-success">
        {{ session('success') }}
    </div>
@endif

<div class="table-container">
    <table>
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Image</th>
                    <th>Name</th>
                    <th>Premium</th>
                    <th>Path</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
            @forelse ($posters as $poster)
                <tr>
                    <td><strong>#{{ $poster->id }}</strong></td>
                    <td>
                        @if($poster->image_path)
                            <img src="{{ Storage::url($poster->image_path) }}" alt="{{ $poster->name }}" class="thumbnail">
                        @else
                            <span style="color: #64748b;">No Image</span>
                        @endif
                    </td>
                    <td><strong>{{ $poster->name }}</strong></td>
                    <td>
                        @if($poster->is_premium)
                            <span class="status-premium">Premium</span>
                        @else
                            <span class="status-regular">Regular</span>
                        @endif
                    </td>
                    <td><code>{{ $poster->image_path }}</code></td>
                    <td>
                        <a href="{{ route('admin.posters.edit', $poster->id) }}" class="btn btn-edit">Edit</a>
                        <form action="{{ route('admin.posters.destroy', $poster->id) }}" method="POST" style="display:inline;">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-delete" onclick="return confirm('Are you sure you want to delete this poster?');">Delete</button>
                        </form>
                    </td>
                </tr>
            @empty
                <tr>
                    <td colspan="6">
                        <div class="empty-state">
                            <h3>No posters found</h3>
                            <p>Start by uploading your first poster template.</p>
                            <a href="{{ route('admin.posters.create') }}" class="btn btn-create">Upload First Poster</a>
                        </div>
                    </td>
                </tr>
            @endforelse
        </tbody>
    </table>
</div>

<div class="pagination">
    {{ $posters->links() }}
</div>
@endsection
