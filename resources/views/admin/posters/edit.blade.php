<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin - Edit Poster</title>
    <style>
        body { font-family: sans-serif; margin: 20px; background-color: #f4f6f9; color: #333; }
        .container { background-color: #fff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); max-width: 600px; margin: auto; }
        h1 { color: #007bff; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input[type="text"], input[type="file"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
            box-sizing: border-box;
        }
        input[type="checkbox"] { margin-right: 5px; vertical-align: middle; }
        img.current-image { max-width: 200px; max-height: 200px; display: block; margin-bottom: 10px; border-radius: 4px; }
        .btn-primary { background-color: #007bff; color: white; padding: 10px 15px; border: none; border-radius: 4px; cursor: pointer; font-size: 16px; }
        .btn-primary:hover { background-color: #0056b3; }
        .alert-error { padding: 10px; background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; border-radius: 4px; margin-bottom: 20px; }
        .link-back { display: block; margin-top: 20px; color: #007bff; text-decoration: none; }
        .link-back:hover { text-decoration: underline; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Edit Poster: {{ $poster->name }}</h1>

        @if ($errors->any())
            <div class="alert-error">
                <ul>
                    @foreach ($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif

        <form action="{{ route('admin.posters.update', $poster->id) }}" method="POST" enctype="multipart/form-data">
            @csrf
            @method('PUT')

            <div class="form-group">
                <label for="name">Poster Name</label>
                <input type="text" name="name" id="name" value="{{ old('name', $poster->name) }}" required>
            </div>

            <div class="form-group">
                <label for="image">Current Poster Image</label>
                @if($poster->image_path)
                    <img src="{{ Storage::url($poster->image_path) }}" alt="{{ $poster->name }}" class="current-image">
                @else
                    <p>No image currently uploaded.</p>
                @endif
                <label for="image_new">Upload New Image (optional)</label>
                <input type="file" name="image" id="image_new">
                <small>Accepted formats: jpeg, png, jpg, gif, svg, webp. Max size: 100MB. Leave empty to keep current image.</small>
            </div>

            <div class="form-group">
                <label for="is_premium">
                    <input type="checkbox" name="is_premium" id="is_premium" value="1" {{ old('is_premium', $poster->is_premium) ? 'checked' : '' }}>
                    Is this a Premium Poster?
                </label>
            </div>

            <button type="submit" class="btn-primary">Update Poster</button>
        </form>
        <a href="{{ route('admin.posters.index') }}" class="link-back">Back to Posters List</a>
    </div>
</body>
</html>
