@extends('layouts.admin')

@section('title', 'Transactions')
@section('page-title', 'Transactions')

@push('styles')
@include('admin.shared-styles')
@endpush

@section('content')
<div class="page-header">
    <h1>💰 Transaction Log</h1>
</div>

@if($transactions->isEmpty())
    <div class="empty-state">
        <h3>No transactions found</h3>
        <p>Transaction history will appear here once users start making payments.</p>
    </div>
@else
    <div class="table-container">
        <table>
            <thead>
                <tr>
                    <th>ID</th>
                    <th>User</th>
                    <th>Amount (INR)</th>
                    <th>Type</th>
                    <th>Description</th>
                    <th>Razorpay Payment ID</th>
                    <th>Date</th>
                </tr>
            </thead>
            <tbody>
                @foreach ($transactions as $transaction)
                    <tr>
                        <td><strong>#{{ $transaction->id }}</strong></td>
                        <td>
                            <strong>{{ $transaction->user ? $transaction->user->name : 'N/A (User deleted?)' }}</strong><br>
                            <small style="color: #64748b;">ID: {{ $transaction->user_id }}</small>
                        </td>
                        <td><strong>₹{{ number_format($transaction->amount, 2) }}</strong></td>
                        <td style="text-transform: capitalize;">{{ $transaction->type }}</td>
                        <td>{{ $transaction->description }}</td>
                        <td><code>{{ $transaction->razorpay_payment_id ?? 'N/A' }}</code></td>
                        <td>{{ $transaction->created_at->format('M d, Y H:i') }}</td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    <div class="pagination">
        {{ $transactions->links() }}
    </div>
@endif
@endsection
