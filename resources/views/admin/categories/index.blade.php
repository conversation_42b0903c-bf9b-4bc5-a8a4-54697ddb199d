@extends('layouts.admin')

@section('title', 'Categories Management')
@section('page-title', 'Categories Management')

@push('styles')
@include('admin.shared-styles')
<style>
    .toggle-switch {
        position: relative;
        display: inline-block;
        width: 60px;
        height: 34px;
    }

    .toggle-switch input {
        opacity: 0;
        width: 0;
        height: 0;
    }

    .slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ccc;
        transition: .4s;
        border-radius: 34px;
    }

    .slider:before {
        position: absolute;
        content: "";
        height: 26px;
        width: 26px;
        left: 4px;
        bottom: 4px;
        background-color: white;
        transition: .4s;
        border-radius: 50%;
    }

    input:checked + .slider {
        background-color: #10b981;
    }

    input:checked + .slider:before {
        transform: translateX(26px);
    }

    .serial-number {
        font-weight: 600;
        color: #667eea;
    }

    .category-hierarchy {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .category-icon {
        font-size: 1.2rem;
    }

    .subcategory {
        padding-left: 2rem;
        position: relative;
    }

    .subcategory::before {
        content: "└─";
        position: absolute;
        left: 0.5rem;
        color: #64748b;
    }

    .category-path {
        color: #64748b;
        font-size: 0.875rem;
    }
</style>
@endpush

@section('content')
<div class="page-header">
    <h1>📂 Manage Categories</h1>
    <a href="{{ route('admin.categories.create') }}" class="btn btn-create">+ Add New Category</a>
</div>

@if(session('success'))
    <div class="alert alert-success">
        {{ session('success') }}
    </div>
@endif

@if(session('error') || $errors->any())
    <div class="alert alert-error">
        @if(session('error'))
            {{ session('error') }}
        @endif
        @if($errors->any())
            <ul>
                @foreach($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        @endif
    </div>
@endif

<div class="table-container">
    <table>
        <thead>
            <tr>
                <th>S.No</th>
                <th>Category</th>
                <th>Slug</th>
                <th>Parent</th>
                <th>Sort Order</th>
                <th>Status</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            @forelse ($categories as $index => $category)
                <tr class="{{ $category->parent_id ? '' : '' }}">
                    <td><span class="serial-number">{{ $categories->firstItem() + $index }}</span></td>
                    <td>
                        <div class="category-hierarchy">
                            @if($category->icon)
                                <span class="category-icon">{{ $category->icon }}</span>
                            @endif
                            <div>
                                <strong>{{ $category->name }}</strong>
                                @if($category->description)
                                    <div class="category-path">{{ Str::limit($category->description, 50) }}</div>
                                @endif
                            </div>
                        </div>
                    </td>
                    <td><code>{{ $category->slug }}</code></td>
                    <td>
                        @if($category->parent)
                            <span class="category-path">{{ $category->parent->name }}</span>
                        @else
                            <span style="color: #64748b;">Root Category</span>
                        @endif
                    </td>
                    <td>{{ $category->sort_order }}</td>
                    <td>
                        <label class="toggle-switch">
                            <input type="checkbox" {{ $category->status ? 'checked' : '' }} 
                                   onchange="toggleStatus({{ $category->id }}, this)">
                            <span class="slider"></span>
                        </label>
                    </td>
                    <td>
                        <a href="{{ route('admin.categories.edit', $category->id) }}" class="btn btn-edit">Edit</a>
                        <form action="{{ route('admin.categories.destroy', $category->id) }}" method="POST" style="display:inline;">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-delete" onclick="return confirm('Are you sure you want to delete this category?');">Delete</button>
                        </form>
                    </td>
                </tr>
            @empty
                <tr>
                    <td colspan="7">
                        <div class="empty-state">
                            <h3>No categories found</h3>
                            <p>Start by creating your first category.</p>
                            <a href="{{ route('admin.categories.create') }}" class="btn btn-create">Create First Category</a>
                        </div>
                    </td>
                </tr>
            @endforelse
        </tbody>
    </table>
</div>

<div class="pagination">
    {{ $categories->links() }}
</div>

@push('scripts')
<script>
function toggleStatus(categoryId, checkbox) {
    const isChecked = checkbox.checked;
    
    fetch(`/admin/categories/${categoryId}/toggle-status`, {
        method: 'PATCH',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            status: isChecked
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log(data.message);
        } else {
            checkbox.checked = !isChecked;
            alert('Failed to update status');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        checkbox.checked = !isChecked;
        alert('An error occurred while updating status');
    });
}
</script>
@endpush
@endsection
