@extends('layouts.admin')

@section('title', 'Edit Category')
@section('page-title', 'Edit Category')

@push('styles')
@include('admin.shared-styles')
@endpush

@section('content')
<div class="page-header">
    <h1>📂 Edit Category: {{ $category->name }}</h1>
    <a href="{{ route('admin.categories.index') }}" class="btn btn-back">← Back to Categories</a>
</div>

@if(session('error'))
    <div class="alert alert-error">
        {{ session('error') }}
    </div>
@endif

@if ($errors->any())
    <div class="alert alert-error">
        <ul>
            @foreach ($errors->all() as $error)
                <li>{{ $error }}</li>
            @endforeach
        </ul>
    </div>
@endif

<div class="form-container">
    <form action="{{ route('admin.categories.update', $category->id) }}" method="POST">
        @csrf
        @method('PUT')

        <div class="form-group">
            <label for="name">Category Name *</label>
            <input type="text" name="name" id="name" value="{{ old('name', $category->name) }}" required>
        </div>

        <div class="form-group">
            <label for="slug">Slug (URL-friendly name)</label>
            <input type="text" name="slug" id="slug" value="{{ old('slug', $category->slug) }}">
            <small style="color: #6b7280; font-size: 0.875rem;">Leave empty to auto-generate from name</small>
        </div>

        <div class="form-group">
            <label for="description">Description</label>
            <textarea name="description" id="description" rows="3">{{ old('description', $category->description) }}</textarea>
        </div>

        <div class="form-group">
            <label for="icon">Icon (Emoji or Icon Class)</label>
            <input type="text" name="icon" id="icon" value="{{ old('icon', $category->icon) }}" placeholder="📂 or fa-folder">
            <small style="color: #6b7280; font-size: 0.875rem;">Use emoji or CSS icon class</small>
        </div>

        <div class="form-group">
            <label for="parent_id">Parent Category</label>
            <select name="parent_id" id="parent_id">
                <option value="">-- Root Category --</option>
                @foreach($parentCategories as $parent)
                    <option value="{{ $parent->id }}" {{ old('parent_id', $category->parent_id) == $parent->id ? 'selected' : '' }}>
                        {{ $parent->name }}
                    </option>
                @endforeach
            </select>
            <small style="color: #6b7280; font-size: 0.875rem;">Select a parent category to create a subcategory</small>
        </div>

        <div class="form-group">
            <label for="sort_order">Sort Order</label>
            <input type="number" name="sort_order" id="sort_order" value="{{ old('sort_order', $category->sort_order) }}" min="0">
            <small style="color: #6b7280; font-size: 0.875rem;">Lower numbers appear first</small>
        </div>

        <div class="form-group">
            <label for="status">Status</label>
            <select name="status" id="status" required>
                <option value="1" {{ old('status', $category->status) == 1 ? 'selected' : '' }}>Active</option>
                <option value="0" {{ old('status', $category->status) == 0 ? 'selected' : '' }}>Inactive</option>
            </select>
        </div>

        <div class="form-actions">
            <button type="submit" class="btn btn-create">Update Category</button>
            <a href="{{ route('admin.categories.index') }}" class="btn btn-back">Cancel</a>
        </div>
    </form>
</div>

@if($category->hasChildren())
<div class="form-container" style="margin-top: 2rem;">
    <h3>Subcategories</h3>
    <div class="table-container">
        <table>
            <thead>
                <tr>
                    <th>Name</th>
                    <th>Slug</th>
                    <th>Status</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                @foreach($category->children as $child)
                    <tr>
                        <td>{{ $child->name }}</td>
                        <td><code>{{ $child->slug }}</code></td>
                        <td>
                            @if($child->status)
                                <span class="status-active">Active</span>
                            @else
                                <span class="status-inactive">Inactive</span>
                            @endif
                        </td>
                        <td>
                            <a href="{{ route('admin.categories.edit', $child->id) }}" class="btn btn-edit">Edit</a>
                        </td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>
</div>
@endif

<script>
document.getElementById('name').addEventListener('input', function() {
    const name = this.value;
    const slugField = document.getElementById('slug');
    
    if (!slugField.value || slugField.dataset.autoGenerated) {
        const slug = name.toLowerCase()
            .replace(/[^a-z0-9\s-]/g, '')
            .replace(/\s+/g, '-')
            .replace(/-+/g, '-')
            .trim('-');
        
        slugField.value = slug;
        slugField.dataset.autoGenerated = 'true';
    }
});

document.getElementById('slug').addEventListener('input', function() {
    if (this.value) {
        delete this.dataset.autoGenerated;
    }
});
</script>
@endsection
