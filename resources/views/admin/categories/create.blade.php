@extends('layouts.admin')

@section('title', 'Create Category')
@section('page-title', 'Create Category')

@push('styles')
@include('admin.shared-styles')
@endpush

@section('content')
<div class="page-header">
    <h1>📂 Create New Category</h1>
    <a href="{{ route('admin.categories.index') }}" class="btn btn-back">← Back to Categories</a>
</div>

@if(session('error'))
    <div class="alert alert-error">
        {{ session('error') }}
    </div>
@endif

@if ($errors->any())
    <div class="alert alert-error">
        <ul>
            @foreach ($errors->all() as $error)
                <li>{{ $error }}</li>
            @endforeach
        </ul>
    </div>
@endif

<div class="form-container">
    <form action="{{ route('admin.categories.store') }}" method="POST">
        @csrf

        <div class="form-group">
            <label for="name">Category Name *</label>
            <input type="text" name="name" id="name" value="{{ old('name') }}" required>
        </div>

        <div class="form-group">
            <label for="slug">Slug (URL-friendly name)</label>
            <input type="text" name="slug" id="slug" value="{{ old('slug') }}">
            <small style="color: #6b7280; font-size: 0.875rem;">Leave empty to auto-generate from name</small>
        </div>

        <div class="form-group">
            <label for="description">Description</label>
            <textarea name="description" id="description" rows="3">{{ old('description') }}</textarea>
        </div>

        <div class="form-group">
            <label for="icon">Icon (Emoji or Icon Class)</label>
            <input type="text" name="icon" id="icon" value="{{ old('icon') }}" placeholder="📂 or fa-folder">
            <small style="color: #6b7280; font-size: 0.875rem;">Use emoji or CSS icon class</small>
        </div>

        <div class="form-group">
            <label for="parent_id">Parent Category</label>
            <select name="parent_id" id="parent_id">
                <option value="">-- Root Category --</option>
                @foreach($parentCategories as $parent)
                    <option value="{{ $parent->id }}" {{ old('parent_id') == $parent->id ? 'selected' : '' }}>
                        {{ $parent->name }}
                    </option>
                @endforeach
            </select>
            <small style="color: #6b7280; font-size: 0.875rem;">Select a parent category to create a subcategory</small>
        </div>

        <div class="form-group">
            <label for="sort_order">Sort Order</label>
            <input type="number" name="sort_order" id="sort_order" value="{{ old('sort_order', 0) }}" min="0">
            <small style="color: #6b7280; font-size: 0.875rem;">Lower numbers appear first</small>
        </div>

        <div class="form-actions">
            <button type="submit" class="btn btn-create">Create Category</button>
            <a href="{{ route('admin.categories.index') }}" class="btn btn-back">Cancel</a>
        </div>
    </form>
</div>

<script>
document.getElementById('name').addEventListener('input', function() {
    const name = this.value;
    const slugField = document.getElementById('slug');
    
    if (!slugField.value || slugField.dataset.autoGenerated) {
        const slug = name.toLowerCase()
            .replace(/[^a-z0-9\s-]/g, '')
            .replace(/\s+/g, '-')
            .replace(/-+/g, '-')
            .trim('-');
        
        slugField.value = slug;
        slugField.dataset.autoGenerated = 'true';
    }
});

document.getElementById('slug').addEventListener('input', function() {
    if (this.value) {
        delete this.dataset.autoGenerated;
    }
});
</script>
@endsection
