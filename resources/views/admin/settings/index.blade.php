@extends('layouts.admin')

@section('title', 'Settings')
@section('page-title', 'Settings')

@push('styles')
@include('admin.shared-styles')
<style>
    .toggle-switch {
        position: relative;
        display: inline-block;
        width: 60px;
        height: 34px;
    }
    .toggle-switch input {
        opacity: 0;
        width: 0;
        height: 0;
    }
    .slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ccc;
        transition: .4s;
        border-radius: 34px;
    }
    .slider:before {
        position: absolute;
        content: "";
        height: 26px;
        width: 26px;
        left: 4px;
        bottom: 4px;
        background-color: white;
        transition: .4s;
        border-radius: 50%;
    }
    input:checked + .slider {
        background-color: #10b981;
    }
    input:checked + .slider:before {
        transform: translateX(26px);
    }
</style>
@endpush

@section('content')
<div class="page-header">
    <h1>⚙️ Application Settings</h1>
</div>

@if(session('success'))
    <div class="alert alert-success">
        {{ session('success') }}
    </div>
@endif

<div class="form-container">
    <form action="{{ route('admin.settings.store') }}" method="POST">
        @csrf

        <!-- Application Settings -->
        <h3 style="margin-bottom: 1.5rem; color: #374151; border-bottom: 2px solid #e5e7eb; padding-bottom: 0.5rem;">📱 Application Settings</h3>

        <div class="form-group">
            <label for="subscription_enabled">Subscription Enabled</label>
            <select name="subscription_enabled" id="subscription_enabled">
                <option value="true" {{ ($settings['subscription_enabled']->setting_value ?? 'false') == 'true' ? 'selected' : '' }}>Enabled</option>
                <option value="false" {{ ($settings['subscription_enabled']->setting_value ?? 'false') == 'false' ? 'selected' : '' }}>Disabled</option>
            </select>
        </div>

        <div class="form-group">
            <label for="pay_per_download_enabled">Pay Per Download Enabled</label>
            <select name="pay_per_download_enabled" id="pay_per_download_enabled">
                <option value="true" {{ ($settings['pay_per_download_enabled']->setting_value ?? 'false') == 'true' ? 'selected' : '' }}>Enabled</option>
                <option value="false" {{ ($settings['pay_per_download_enabled']->setting_value ?? 'false') == 'false' ? 'selected' : '' }}>Disabled</option>
            </select>
        </div>

        <div class="form-group">
            <label for="per_download_cost">Per Download Cost (INR)</label>
            <input type="number" name="per_download_cost" id="per_download_cost" value="{{ $settings['per_download_cost']->setting_value ?? '1.00' }}" step="0.01">
        </div>

        <div class="form-group">
            <label for="free_download_enabled">Free Download Enabled</label>
            <select name="free_download_enabled" id="free_download_enabled">
                <option value="true" {{ ($settings['free_download_enabled']->setting_value ?? 'true') == 'true' ? 'selected' : '' }}>Enabled</option>
                <option value="false" {{ ($settings['free_download_enabled']->setting_value ?? 'true') == 'false' ? 'selected' : '' }}>Disabled</option>
            </select>
        </div>

        <!-- Google OAuth Settings -->
        <h3 style="margin: 2rem 0 1.5rem 0; color: #374151; border-bottom: 2px solid #e5e7eb; padding-bottom: 0.5rem;">🔐 Google OAuth Settings</h3>

        <div class="form-group">
            <label for="google_client_id">Google Client ID</label>
            <input type="text" name="google_client_id" id="google_client_id" value="{{ $settings['google_client_id']->setting_value ?? '' }}" placeholder="Enter Google OAuth Client ID">
            <small style="color: #6b7280; font-size: 0.875rem;">Get this from Google Cloud Console → APIs & Services → Credentials</small>
        </div>

        <div class="form-group">
            <label for="google_client_secret">Google Client Secret</label>
            <input type="password" name="google_client_secret" id="google_client_secret" value="{{ $settings['google_client_secret']->setting_value ?? '' }}" placeholder="Enter Google OAuth Client Secret">
            <small style="color: #6b7280; font-size: 0.875rem;">Keep this secret and secure</small>
        </div>

        <div class="form-group">
            <label for="google_redirect_url">Google Redirect URL</label>
            <input type="url" name="google_redirect_url" id="google_redirect_url" value="{{ $settings['google_redirect_url']->setting_value ?? '' }}" placeholder="http://127.0.0.1:8000/auth/callback/google">
            <small style="color: #6b7280; font-size: 0.875rem;">Must match the redirect URI in Google Cloud Console</small>
        </div>

        <!-- Razorpay Settings -->
        <h3 style="margin: 2rem 0 1.5rem 0; color: #374151; border-bottom: 2px solid #e5e7eb; padding-bottom: 0.5rem;">💳 Razorpay Payment Settings</h3>

        <div class="form-group">
            <label for="razorpay_key_id">Razorpay Key ID</label>
            <input type="text" name="razorpay_key_id" id="razorpay_key_id" value="{{ $settings['razorpay_key_id']->setting_value ?? '' }}" placeholder="rzp_test_xxxxxxxxxx">
            <small style="color: #6b7280; font-size: 0.875rem;">Get this from Razorpay Dashboard → Settings → API Keys</small>
        </div>

        <div class="form-group">
            <label for="razorpay_key_secret">Razorpay Key Secret</label>
            <input type="password" name="razorpay_key_secret" id="razorpay_key_secret" value="{{ $settings['razorpay_key_secret']->setting_value ?? '' }}" placeholder="Enter Razorpay Key Secret">
            <small style="color: #6b7280; font-size: 0.875rem;">Keep this secret and secure</small>
        </div>

        <div class="form-group">
            <label for="razorpay_webhook_secret">Razorpay Webhook Secret</label>
            <input type="password" name="razorpay_webhook_secret" id="razorpay_webhook_secret" value="{{ $settings['razorpay_webhook_secret']->setting_value ?? '' }}" placeholder="Enter Webhook Secret">
            <small style="color: #6b7280; font-size: 0.875rem;">Used to verify webhook authenticity</small>
        </div>

        <div class="form-actions">
            <button type="submit" class="btn btn-create">💾 Save Settings</button>
        </div>
    </form>
</div>
@endsection
