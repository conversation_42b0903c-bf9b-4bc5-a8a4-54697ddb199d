@extends('layouts.admin')

@section('title', 'Settings')
@section('page-title', 'Settings')

@push('styles')
@include('admin.shared-styles')
<style>
    .toggle-switch {
        position: relative;
        display: inline-block;
        width: 60px;
        height: 34px;
    }
    .toggle-switch input {
        opacity: 0;
        width: 0;
        height: 0;
    }
    .slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ccc;
        transition: .4s;
        border-radius: 34px;
    }
    .slider:before {
        position: absolute;
        content: "";
        height: 26px;
        width: 26px;
        left: 4px;
        bottom: 4px;
        background-color: white;
        transition: .4s;
        border-radius: 50%;
    }
    input:checked + .slider {
        background-color: #10b981;
    }
    input:checked + .slider:before {
        transform: translateX(26px);
    }
</style>
@endpush

@section('content')
<div class="page-header">
    <h1>⚙️ Application Settings</h1>
</div>

@if(session('success'))
    <div class="alert alert-success">
        {{ session('success') }}
    </div>
@endif

<div class="form-container">
    <form action="{{ route('admin.settings.store') }}" method="POST">
        @csrf

        <div class="form-group">
            <label for="subscription_enabled">Subscription Enabled</label>
            <select name="subscription_enabled" id="subscription_enabled">
                <option value="true" {{ ($settings['subscription_enabled']->setting_value ?? 'false') == 'true' ? 'selected' : '' }}>Enabled</option>
                <option value="false" {{ ($settings['subscription_enabled']->setting_value ?? 'false') == 'false' ? 'selected' : '' }}>Disabled</option>
            </select>
        </div>

        <div class="form-group">
            <label for="pay_per_download_enabled">Pay Per Download Enabled</label>
            <select name="pay_per_download_enabled" id="pay_per_download_enabled">
                <option value="true" {{ ($settings['pay_per_download_enabled']->setting_value ?? 'false') == 'true' ? 'selected' : '' }}>Enabled</option>
                <option value="false" {{ ($settings['pay_per_download_enabled']->setting_value ?? 'false') == 'false' ? 'selected' : '' }}>Disabled</option>
            </select>
        </div>

        <div class="form-group">
            <label for="per_download_cost">Per Download Cost (INR)</label>
            <input type="number" name="per_download_cost" id="per_download_cost" value="{{ $settings['per_download_cost']->setting_value ?? '1.00' }}" step="0.01">
        </div>

        <div class="form-group">
            <label for="free_download_enabled">Free Download Enabled</label>
            <select name="free_download_enabled" id="free_download_enabled">
                <option value="true" {{ ($settings['free_download_enabled']->setting_value ?? 'true') == 'true' ? 'selected' : '' }}>Enabled</option>
                <option value="false" {{ ($settings['free_download_enabled']->setting_value ?? 'true') == 'false' ? 'selected' : '' }}>Disabled</option>
            </select>
        </div>

        <div class="form-actions">
            <button type="submit" class="btn btn-primary">💾 Save Settings</button>
        </div>
    </form>
</div>
@endsection
