@extends('layouts.admin')

@section('title', 'Dashboard')
@section('page-title', 'Dashboard')

@push('styles')
<style>
    .welcome-section {
        text-align: center;
        margin-bottom: 2rem;
        padding: 2rem;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 1rem;
        color: white;
        box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
    }

    .welcome-section h2 {
        font-size: 2rem;
        margin-bottom: 0.5rem;
        font-weight: 600;
    }

    .welcome-section p {
        font-size: 1.1rem;
        opacity: 0.9;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .stat-card {
        background: white;
        padding: 2rem;
        border-radius: 1rem;
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        text-align: center;
        border: 1px solid #e2e8f0;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #667eea, #764ba2);
    }

    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 30px rgba(0,0,0,0.12);
    }

    .stat-card h3 {
        color: #64748b;
        font-size: 0.875rem;
        margin-bottom: 0.75rem;
        text-transform: uppercase;
        letter-spacing: 1px;
        font-weight: 600;
    }

    .stat-card .number {
        font-size: 2.5rem;
        font-weight: 700;
        color: #1e293b;
        margin-bottom: 0.5rem;
    }

    .stat-card .label {
        color: #64748b;
        font-size: 0.875rem;
    }

    .admin-menu {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 1.5rem;
    }

    .menu-card {
        background: white;
        padding: 2rem;
        border-radius: 1rem;
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        border: 1px solid #e2e8f0;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .menu-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #10b981, #059669);
    }

    .menu-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 30px rgba(0,0,0,0.12);
    }

    .menu-card h3 {
        color: #1e293b;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 0.75rem;
        font-size: 1.25rem;
        font-weight: 600;
    }

    .menu-card p {
        color: #64748b;
        margin-bottom: 1.5rem;
        line-height: 1.6;
    }

    .menu-card .actions {
        display: flex;
        gap: 0.75rem;
        flex-wrap: wrap;
    }

    .menu-card .btn {
        padding: 0.75rem 1.25rem;
        border: none;
        border-radius: 0.5rem;
        text-decoration: none;
        font-size: 0.875rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        display: inline-block;
        text-align: center;
    }
</style>
@endpush

@section('content')
<div class="welcome-section">
    <h2>Welcome to Admin Dashboard</h2>
    <p>Manage your social media poster application from here</p>
</div>

<div class="stats-grid">
    <div class="stat-card">
        <h3>Total Users</h3>
        <div class="number">{{ \App\Models\User::count() }}</div>
        <div class="label">Registered Users</div>
    </div>
    <div class="stat-card">
        <h3>Total Plans</h3>
        <div class="number">{{ \App\Models\Plan::count() }}</div>
        <div class="label">Subscription Plans</div>
    </div>
    <div class="stat-card">
        <h3>Total Posters</h3>
        <div class="number">{{ \App\Models\Poster::count() }}</div>
        <div class="label">Poster Templates</div>
    </div>
    <div class="stat-card">
        <h3>Total Frames</h3>
        <div class="number">{{ \App\Models\Frame::count() }}</div>
        <div class="label">Frame Templates</div>
    </div>
    <div class="stat-card">
        <h3>Total Categories</h3>
        <div class="number">{{ \App\Models\Category::count() }}</div>
        <div class="label">Content Categories</div>
    </div>
</div>
<div class="admin-menu">
    <div class="menu-card">
        <h3>📋 Plans Management</h3>
        <p>Create and manage subscription plans for users</p>
        <div class="actions">
            <a href="{{ route('admin.plans.index') }}" class="btn btn-primary">View Plans</a>
            <a href="{{ route('admin.plans.create') }}" class="btn btn-success">Add New Plan</a>
        </div>
    </div>

    <div class="menu-card">
        <h3>🖼️ Posters Management</h3>
        <p>Upload and manage poster templates</p>
        <div class="actions">
            <a href="{{ route('admin.posters.index') }}" class="btn btn-primary">View Posters</a>
            <a href="{{ route('admin.posters.create') }}" class="btn btn-success">Add New Poster</a>
        </div>
    </div>

    <div class="menu-card">
        <h3>🖼️ Frames Management</h3>
        <p>Upload and manage frame templates</p>
        <div class="actions">
            <a href="{{ route('admin.frames.index') }}" class="btn btn-primary">View Frames</a>
            <a href="{{ route('admin.frames.create') }}" class="btn btn-success">Add New Frame</a>
        </div>
    </div>

    <div class="menu-card">
        <h3>📂 Categories Management</h3>
        <p>Create and manage content categories</p>
        <div class="actions">
            <a href="{{ route('admin.categories.index') }}" class="btn btn-primary">View Categories</a>
            <a href="{{ route('admin.categories.create') }}" class="btn btn-success">Add New Category</a>
        </div>
    </div>

    <div class="menu-card">
        <h3>💰 Transactions</h3>
        <p>View all payment transactions</p>
        <div class="actions">
            <a href="{{ route('admin.transactions.index') }}" class="btn btn-info">View Transactions</a>
        </div>
    </div>

    <div class="menu-card">
        <h3>⚙️ Settings</h3>
        <p>Configure application settings</p>
        <div class="actions">
            <a href="{{ route('admin.settings.index') }}" class="btn btn-info">App Settings</a>
        </div>
    </div>
</div>
@endsection
