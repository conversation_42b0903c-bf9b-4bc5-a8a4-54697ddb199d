<script>
// Cascading Category Dropdown Functionality
function initializeCategoryDropdowns() {
    const categorySelect = document.getElementById('category_id');
    const subcategorySelect = document.getElementById('subcategory_id');
    
    if (!categorySelect || !subcategorySelect) {
        return;
    }

    // Function to load subcategories
    function loadSubcategories(parentId) {
        // Clear current subcategory options
        subcategorySelect.innerHTML = '<option value="">-- Select Subcategory (Optional) --</option>';
        subcategorySelect.disabled = true;

        if (!parentId) {
            return;
        }

        // Show loading state
        subcategorySelect.innerHTML = '<option value="">Loading...</option>';

        // Fetch subcategories
        fetch(`/admin/categories/subcategories?parent_id=${parentId}`, {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            // Clear loading state
            subcategorySelect.innerHTML = '<option value="">-- Select Subcategory (Optional) --</option>';
            
            // Add subcategory options
            data.forEach(subcategory => {
                const option = document.createElement('option');
                option.value = subcategory.id;
                option.textContent = subcategory.name;
                subcategorySelect.appendChild(option);
            });

            // Enable subcategory dropdown if there are options
            subcategorySelect.disabled = data.length === 0;
        })
        .catch(error => {
            console.error('Error loading subcategories:', error);
            subcategorySelect.innerHTML = '<option value="">Error loading subcategories</option>';
        });
    }

    // Event listener for category change
    categorySelect.addEventListener('change', function() {
        const selectedCategoryId = this.value;
        loadSubcategories(selectedCategoryId);
    });

    // Initialize subcategories if category is already selected (for edit forms)
    const initialCategoryId = categorySelect.value;
    if (initialCategoryId) {
        loadSubcategories(initialCategoryId);
        
        // Set the selected subcategory after loading (for edit forms)
        const initialSubcategoryId = subcategorySelect.getAttribute('data-selected');
        if (initialSubcategoryId) {
            setTimeout(() => {
                subcategorySelect.value = initialSubcategoryId;
            }, 500); // Wait for subcategories to load
        }
    }
}

// Advanced Filtering Functionality
function initializeAdvancedFilters() {
    const filterForm = document.getElementById('filter-form');
    const clearFiltersBtn = document.getElementById('clear-filters');
    const resultsContainer = document.getElementById('results-container');
    
    if (!filterForm || !resultsContainer) {
        return;
    }

    // Function to apply filters
    function applyFilters() {
        const formData = new FormData(filterForm);
        const params = new URLSearchParams();
        
        // Build query parameters
        for (let [key, value] of formData.entries()) {
            if (value.trim() !== '') {
                params.append(key, value);
            }
        }

        // Update URL with filter parameters
        const newUrl = window.location.pathname + '?' + params.toString();
        window.history.pushState({}, '', newUrl);

        // Show loading state
        resultsContainer.style.opacity = '0.5';
        
        // Fetch filtered results
        fetch(newUrl, {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.text())
        .then(html => {
            // Parse the response and extract the results
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');
            const newResults = doc.getElementById('results-container');
            
            if (newResults) {
                resultsContainer.innerHTML = newResults.innerHTML;
                
                // Reinitialize toggle switches
                initializeToggleSwitches();
                
                // Update filter count
                updateFilterCount();
            }
            
            resultsContainer.style.opacity = '1';
        })
        .catch(error => {
            console.error('Error applying filters:', error);
            resultsContainer.style.opacity = '1';
        });
    }

    // Function to clear all filters
    function clearFilters() {
        filterForm.reset();
        
        // Reset category dropdowns
        const categorySelect = document.getElementById('filter_category_id');
        const subcategorySelect = document.getElementById('filter_subcategory_id');
        
        if (categorySelect) categorySelect.value = '';
        if (subcategorySelect) {
            subcategorySelect.innerHTML = '<option value="">-- All Subcategories --</option>';
            subcategorySelect.disabled = true;
        }
        
        // Apply filters (which will now be empty)
        applyFilters();
    }

    // Function to update filter count
    function updateFilterCount() {
        const filterCount = document.getElementById('filter-count');
        if (!filterCount) return;
        
        const formData = new FormData(filterForm);
        let count = 0;
        
        for (let [key, value] of formData.entries()) {
            if (value.trim() !== '') {
                count++;
            }
        }
        
        filterCount.textContent = count;
        filterCount.style.display = count > 0 ? 'inline' : 'none';
    }

    // Event listeners for filter inputs
    const filterInputs = filterForm.querySelectorAll('input, select');
    filterInputs.forEach(input => {
        input.addEventListener('change', applyFilters);
        if (input.type === 'text') {
            let timeout;
            input.addEventListener('input', () => {
                clearTimeout(timeout);
                timeout = setTimeout(applyFilters, 500); // Debounce text input
            });
        }
    });

    // Clear filters button
    if (clearFiltersBtn) {
        clearFiltersBtn.addEventListener('click', clearFilters);
    }

    // Initialize filter count
    updateFilterCount();

    // Initialize category dropdown for filters
    const filterCategorySelect = document.getElementById('filter_category_id');
    const filterSubcategorySelect = document.getElementById('filter_subcategory_id');
    
    if (filterCategorySelect && filterSubcategorySelect) {
        filterCategorySelect.addEventListener('change', function() {
            const selectedCategoryId = this.value;
            
            // Clear subcategory options
            filterSubcategorySelect.innerHTML = '<option value="">-- All Subcategories --</option>';
            filterSubcategorySelect.disabled = true;

            if (!selectedCategoryId) {
                return;
            }

            // Load subcategories for filter
            fetch(`/admin/categories/subcategories?parent_id=${selectedCategoryId}`, {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                data.forEach(subcategory => {
                    const option = document.createElement('option');
                    option.value = subcategory.id;
                    option.textContent = subcategory.name;
                    filterSubcategorySelect.appendChild(option);
                });

                filterSubcategorySelect.disabled = data.length === 0;
            })
            .catch(error => {
                console.error('Error loading filter subcategories:', error);
            });
        });
    }
}

// Toggle switches functionality
function initializeToggleSwitches() {
    // This function will be called to reinitialize toggle switches after AJAX updates
    // The actual toggle functionality should be defined in the specific page scripts
}

// Initialize everything when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeCategoryDropdowns();
    initializeAdvancedFilters();
});
</script>
